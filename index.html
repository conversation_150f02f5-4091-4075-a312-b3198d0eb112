<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="UTF-8">
	<title>2026年公务员模考大赛-中公杯模考-国省考资料-国省考培训用书</title>
	<meta name="description" content="2026公务员招聘即将发布，为了帮助广大考生有效备考国省考，中公教育特举办中公杯模王争霸赛，通过3轮模考梳理出自己的短板然后针对性备考，争取帮助考生顺利考过。参加模考还能听课程、领取20本开学定制资料，考的好还能领学习机等大奖" />
	<meta name="keywords" content="2026公务员考试、中公杯、公务员招聘、国省考、国家公务员考试、公务员备考、公务员资料" />
	<link rel="stylesheet" href="css/public.css">
	<link rel="stylesheet" href="css/animate.min.css">
	<link rel="stylesheet" href="css/style.css?v=2">
	<link rel="stylesheet" href="css/swiper.css">
	<script type="text/javascript" src="js/jquery-1.8.3.min.js"></script>
	<script type="text/javascript" src="js/wow.min.js"></script>
	<script type="text/javascript" src="https://www.offcn.com/statics/js/bdtop.js"></script>
	<!-- <script>
        try {
            const urlhash = window.location.hash;
            if (!urlhash.match("fromapp")) {
                if ((navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i))) {
                    window.location.href = './wap/index.html' + window.location.search;
                }
            }
        } catch (err) {
        }
    </script> -->
	<script type="text/javascript">
		new WOW().init();
	</script>
</head>

<body>
	<!--头部-->
	<div class="zg_head clearfix">
		<div class="zg_w1200">
			<div class="zg_headdiv">
				<a href="https://www.offcn.com/" target="_blank" class="zg_logo" title="中公教育官网">
					<img src="images/zg_logo.png" width="100%">
				</a>
				<div class="zg_head_nav">
					<a href="https://www.offcn.com/" target="_blank" title="中公教育官网">中公首页</a><span>|</span>
					<a href="https://learn.offcn.com/" target="_blank">直播课堂</a><span>|</span>
					<a href="https://www.letushu.com/" target="_blank" rel="nofollow">图书商城</a><span>|</span>
					<a href="http://www.eoffcn.com/" target="_blank">网校课程</a><span>|</span>
					<a href="https://www.offcn.com/about/contact.html" target="_blank">联系我们</a>
				</div>
			</div>
			<div class="zg_head_right">
				<a href="javascript:void(0);" class="zg_topTel">全国统一咨询电话：400-6300-999</a>
			</div>
		</div>
	</div>
	<!--头部结束-->
	<div class="zg_banner clearfix">
		<div class="zg_w1200 zg_ban_h clearfix">
			<div class="banner">
				<div class="banner-top fadeInUp wow" data-wow-delay="0.1s"><img src="./images/banner-top.png" alt=""></div>
				<div class="banner-title zoomInUp wow" data-wow-delay="0.2s"><img src="./images/banner-title1.png"
						alt=""></div>
				<div class="banner-subtitle zoomInUp wow" data-wow-delay="0.3s"><img src="./images/banner-title2.png"
						alt=""></div>
				<div class="banner-bottom">
					<div class="banner-bottom1 fadeInUp wow" data-wow-delay="0.4s">中公自研<b>AI模考数据库</b></div>
					<div class="banner-bottom2 fadeInUp wow" data-wow-delay="0.4s"><b>3轮模考 3轮好课 3轮好礼</b> 助力公考未来</div>
				</div>
			</div>
		</div>
	</div>
	<!-- banner1结束 -->
	<!--内容部分-->
	<div class="zg_wrap clearfix">
		<div class="zg_w1200 clearfix">
			<div class="nav">
				<div class="nav-item">
					<div class="nav-box">
						<div class="nav-item-subtitle">第1轮 - 8月23日</div>
						<div class="nav-item-title">考试能力自测模考</div>
						<div class="nav-item-line"></div>
						<div class="nav-item-box">
							<div class="nav-item-sub active">听</div>
							<div class="nav-item-sub">考</div>
							<div class="nav-item-sub">奖</div>
						</div>
					</div>
				</div>
				<div class="nav-item">
					<div class="nav-box">
						<div class="nav-item-subtitle">第2轮 - 9月13日</div>
						<div class="nav-item-title">万人模考大赛</div>
						<div class="nav-item-line"></div>
						<div class="nav-item-box">
							<div class="nav-item-sub">听</div>
							<div class="nav-item-sub">考</div>
							<div class="nav-item-sub">奖</div>
						</div>
					</div>
				</div>
				<div class="nav-item">
					<div class="nav-box">
						<div class="nav-item-subtitle">第3轮 - 9月27日</div>
						<div class="nav-item-title">终极模王争霸</div>
						<div class="nav-item-line"></div>
						<div class="nav-item-box">
							<div class="nav-item-sub">听</div>
							<div class="nav-item-sub">考</div>
							<div class="nav-item-sub">奖</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row tac">
				<div class="nav-title col">挑战公考<span>150+</span></div>
			</div>
			<div class="nav-line"><img src="./images/part-nav-line.png" alt=""></div>

			<div>
				<div class="section-content active">
					<div class="part1">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title1.png" alt=""></div>
								<div class="part-title-sub">就业&备考第一课  从中公杯开始</div>
							</div>
							<div class="part-table tac">
								<div class="part-table-col1">
									<div class="part-table-title">课程内容</div>
									<div class="part-table-content"><span>选方向</span>-探寻优就业方向，公考热门岗位分析</div>
									<div class="part-table-content"><span>找优势</span>-应届生身份报考国省考要把握住这8大优势</div>
									<div class="part-table-content"><span>抓好岗</span>-国省岗位怎么选？选对的更要选发展好的<div class="part-table-line">8月20日一模</div></div>
									<div class="part-table-content"><span>备考逻辑</span>-政治素养备考核心</div>
									<div class="part-table-content"><span>备考逻辑</span>-找对方法轻松解行测理</div>
									<div class="part-table-content"><span>备考逻辑</span>-掌握行测文的作答策略，一眼洞悉题干关系</div>
									<div class="part-table-content"><span>备考逻辑</span>-怎么用材料"拼出申论"（非作文）</div>
									<div class="part-table-content"><span>备考逻辑</span>-文章意为高，笔落惊风雨（作文题）<div class="part-table-line">9月13日二模</div></div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测文 5大技巧点拨</div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测理 6大技巧点拨</div>
									<div class="part-table-content"><span>申论技巧提升</span>-采分要点护抓住，标答技巧有高招<div class="part-table-line">9月27日三模</div></div>
								</div>
								<div class="part-table-col2">
									<div class="part-table-title part-table-title2">上课时间</div>
									<div class="part-table-content">8月12日</div>
									<div class="part-table-content">8月13日</div>
									<div class="part-table-content">8月14日</div>
									<div class="part-table-content">9月8日</div>
									<div class="part-table-content">9月9日</div>
									<div class="part-table-content">9月10日</div>
									<div class="part-table-content">9月11日</div>
									<div class="part-table-content">9月12日</div>
									<div class="part-table-content">9月17日</div>
									<div class="part-table-content">9月18日</div>
									<div class="part-table-content">9月19日</div>
								</div>
								<a href="#" target="_blank" class="part-table-col3 part-table-title part-table-title3">公<br>职<br>就<br>业<br>&<br>备<br>考<br>系<br>列<br>讲<br>座<div class="part-table-pirce bounceY"><img src="./images/price-0.1.png" alt=""></div></a>
							</div>
							<div class="part-book">
								<div class="part-book-link bounceY"><img src="./images/price.png" alt=""></div>
								<div class="row">
									<div class="part-book-title col">中公金牌示范课，邀你来听！</div>
								</div>
								<div class="part-book-content">
									<div class="part-book-content-title"><span>全新设计</span>试听内容</div>
									<div class="part-book-content-subtitle">听10分钟也能让你有所收获</div>
									<div class="part-book-content-info">打破行业试听枯燥、授课太过基础的窘境<br>短时高效感受试题变化，快速获取解题技巧</div>
									<div class="part-book-content-desc">备考不再是拼刷题量的时代<br>拼的是<span>心眼子</span></div>
									<div class="part-book-content-btn part-link part-btn"><img src="./images/part-book-btn.png" alt=""></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="section-content">
					<div class="part2">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title2.png" alt=""></div>
								<div class="part-title-sub">考试能力自测模考</div>
							</div>
							<div class="row tac">
								<div class="col part2-box">
									<div class="part2-box-title">开放领取试卷</div>
									<div class="part2-box-subtitle">8月20日08:00</div>
								</div>
								<div class="col part2-box">
									<div class="part2-box-title">试题解析直播</div>
									<div class="part2-box-subtitle">8月24日（周日）<br><span>申论09:30-12:00  行测13:30-18:00</span></div>
								</div>
							</div>
							<div class="part-paper">
								<div class="row">
									<div class="col part2-paper-title">★紧扣新大纲</div>
									<div class="col part2-paper-subtitle">强化政治综合能力</div>
								</div>
								<div class="row">
									<div class="col part2-paper-title">★中公实力研发</div>
									<div class="col part2-paper-subtitle">明师模拟出题，尽力贴合考试内容</div>
								</div>
								<div class="row">
									<div class="col part2-paper-title">★考后解析直播</div>
									<div class="col part2-paper-subtitle">逐题精讲，梳理考试重难点</div>
								</div>
								<a href="#" target="_blank" class="part-paper-btn part-btn"><img src="./images/part-paper-btn.png" alt=""></a>
							</div>
							<div class="part-class">
								<div class="row">
									<div class="part-book-title col">线上线下同步模考</div>
								</div>
								<div class="part-class-content">
									<div class="part-class-content-title">全真模拟，<span>高度还原</span>高压考场！</div>
									<div class="part-class-content-subtitle">考完试<b>当场对答案</b>，回收试卷<b>明师分析</b><br>到店模考<b>惊喜不断</b>，更多严选<b>备考礼包</b>等你来拿！</div>
									<div class="part-class-content-btn part-btn part-link"><img src="./images/part-class-btn.png" alt=""></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="section-content">
					<div class="part3">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title3.png" alt=""></div>
								<div class="part-title-sub">热血开学季<br>领<span>20</span>本开学定制教案</div>
							</div>
							<div class="part-teach">
								<div class="part-teach-btn part-link part-btn"><img src="./images/part-teach-btn.png" alt=""></div>
							</div>
						</div>
					</div>
				</div>
				<div class="section-content">
					<div class="part1" data-date="2025-9-13">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title1.png" alt=""></div>
								<div class="part-title-sub">就业&备考第一课  从中公杯开始</div>
							</div>
							<div class="part-table tac">
								<div class="part-table-col1">
									<div class="part-table-title">课程内容</div>
									<div class="part-table-content"><span>选方向</span>-探寻优就业方向，公考热门岗位分析</div>
									<div class="part-table-content"><span>找优势</span>-应届生身份报考国省考要把握住这8大优势</div>
									<div class="part-table-content"><span>抓好岗</span>-国省岗位怎么选？选对的更要选发展好的<div class="part-table-line">8月20日一模</div></div>
									<div class="part-table-content"><span>备考逻辑</span>-政治素养备考核心</div>
									<div class="part-table-content"><span>备考逻辑</span>-找对方法轻松解行测理</div>
									<div class="part-table-content"><span>备考逻辑</span>-掌握行测文的作答策略，一眼洞悉题干关系</div>
									<div class="part-table-content"><span>备考逻辑</span>-怎么用材料"拼出申论"（非作文）</div>
									<div class="part-table-content"><span>备考逻辑</span>-文章意为高，笔落惊风雨（作文题）<div class="part-table-line">9月13日二模</div></div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测文 5大技巧点拨</div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测理 6大技巧点拨</div>
									<div class="part-table-content"><span>申论技巧提升</span>-采分要点护抓住，标答技巧有高招<div class="part-table-line">9月27日三模</div></div>
								</div>
								<div class="part-table-col2">
									<div class="part-table-title part-table-title2">上课时间</div>
									<div class="part-table-content">8月12日</div>
									<div class="part-table-content">8月13日</div>
									<div class="part-table-content">8月14日</div>
									<div class="part-table-content">9月8日</div>
									<div class="part-table-content">9月9日</div>
									<div class="part-table-content">9月10日</div>
									<div class="part-table-content">9月11日</div>
									<div class="part-table-content">9月12日</div>
									<div class="part-table-content">9月17日</div>
									<div class="part-table-content">9月18日</div>
									<div class="part-table-content">9月19日</div>
								</div>
								<a href="#" target="_blank" class="part-table-col3 part-table-title part-table-title3">公<br>职<br>就<br>业<br>&<br>备<br>考<br>系<br>列<br>讲<br>座<div class="part-table-pirce bounceY"><img src="./images/price-0.1.png" alt=""></div></a>
							</div>
							<div class="part-book">
								<div class="part-book-link bounceY"><img src="./images/price.png" alt=""></div>
								<div class="row">
									<div class="part-book-title col">中公金牌示范课，邀你来听！</div>
								</div>
								<div class="part-book-content">
									<div class="part-book-content-title"><span>全新设计</span>试听内容</div>
									<div class="part-book-content-subtitle">听10分钟也能让你有所收获</div>
									<div class="part-book-content-info">打破行业试听枯燥、授课太过基础的窘境<br>短时高效感受试题变化，快速获取解题技巧</div>
									<div class="part-book-content-desc">备考不再是拼刷题量的时代<br>拼的是<span>心眼子</span></div>
									<div class="part-book-content-btn part-link part-btn"><img src="./images/part-book-btn.png" alt=""></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-13">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
				<div class="section-content">
					<div class="part2" data-date="2025-9-13">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title2.png" alt=""></div>
								<div class="part-title-sub">万人模考大赛</div>
							</div>
							<div class="part2-subtitle">知己知彼定位弱项·考分排名赢大奖！</div>
							<div class="part-wr clearfix">
								<div class="part-wr-link bounceY"><img src="./images/price-0.png" alt=""></div>
								<div class="part-wr1 fl">
									<div class="part-wr-top">
										<div class="part-wr-title">中公自研AI模考数据库</div>
										<div class="part-wr-subtitle">严选历年试题贴合考情</div>
									</div>
									<div>
										<div class="part-wr-title">明师逐题精讲解析</div>
										<div class="part-wr-subtitle">点拨重难，排除失分弱项</div>
									</div>
								</div>
								<div class="part-wr2 fr">
									<div class="part-wr-top">
										<div class="part-wr-title">严审考题考卷</div>
										<div class="part-wr-subtitle">三审三教保证质量</div>
									</div>
									<div>
										<div class="part-wr-title">考后立即出分</div>
										<div class="part-wr-subtitle">及时了解自身水平</div>
									</div>
								</div>
							</div>
							<div class="part-mk clearfix">
								<div class="fl part-mk-left">
									<div class="row tac">
										<div class="part-mk-qr col"><img src="./images/part-mk-qr.png" alt=""></div>
									</div>
									<div class="row tac">
										<div class="col part-mk-qr-title">扫码下载中公教育APP</div>
									</div>
								</div>
								<div class="fr part-mk-right">
									<div class="row part-mk-item">
										<div class="col part-mk-title">考试时间</div>
										<div class="col part-mk-subtitle">9月13日   [行测] 09:00-11:00   [申论] 14:00-17:00</div>
									</div>
									<div class="row part-mk-item">
										<div class="col part-mk-title">解析时间</div>
										<div class="col part-mk-subtitle">9月14日   [申论] 09:30-12:00   [行测] 13:30-18:00</div>
									</div>
									<div class="row part-mk-item">
										<div class="col part-mk-title">参与方式</div>
										<div class="col part-mk-subtitle">下载中公教育APP，进入国考/本省[刷题] 页面，点击[模考 大赛]或【中公杯万人模考大赛】提前抢座<br>按时间节点等待模考即可</div>
									</div>
								</div>
							</div>
							<div class="part-class">
								<div class="row">
									<div class="part-book-title col">线上线下同步模考</div>
								</div>
								<div class="part-class-content">
									<div class="part-class-content-title">全真模拟，<span>高度还原</span>高压考场！</div>
									<div class="part-class-content-subtitle">考完试<b>当场对答案</b>，回收试卷<b>明师分析</b><br>到店模考<b>惊喜不断</b>，更多严选<b>备考礼包</b>等你来拿！</div>
									<div class="part-class-content-btn part-link part-btn"><img src="./images/part-class-btn.png" alt=""></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-13">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
				<div class="section-content">
					<div class="part6" data-date="2025-9-13">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title3.png" alt=""></div>
								<div class="part-title-sub">中公APP线上模考 排名赢大奖</div>
							</div>
							<div class="part6-content">
								<div class="clearfix">
									<div class="fr part6-award">
										<div class="part6-item">
											<div class="part6-item-top">第1-5名</div>
											<div class="part6-item-title">中公AI学习机一台</div>
											<div class="part6-item-subtitle">价值4699元</div>
										</div>
										<div class="part6-item">
											<div class="part6-item-top">第6-15名</div>
											<div class="part6-item-title">2026国考笔面深度系统班</div>
											<div class="part6-item-subtitle">价值980元</div>
										</div>
										<div class="part6-item">
											<div class="part6-item-top">第16-35名</div>
											<div class="part6-item-title">国考精选考前冲刺视频班-全科</div>
											<div class="part6-item-subtitle">价值880元</div>
										</div>
									</div>
								</div>
								<div class="part6-bottom row tac">
									<div class="col part6-list">
										<div class="part6-list-num">第36-65名</div>
										<div class="part6-list-title">李琳亲授行测<br>重难点点拨课</div>
										<div class="part6-list-pic"><img src="./images/part6-pic1.png" alt=""></div>
										<div class="part6-list-price">价值199元</div>
									</div>
									<div class="col part6-list">
										<div class="part6-list-num">第66-80名</div>
										<div class="part6-list-title">中公APP申论<br>行测会员3个月</div>
										<div class="part6-list-pic"><img src="./images/part6-pic2.png" alt=""></div>
										<div class="part6-list-price">价值118元</div>
									</div>
									<div class="col part6-list">
										<div class="part6-list-num">第81-85名</div>
										<div class="part6-list-title">中公国考笔试<br>图书套装</div>
										<div class="part6-list-pic"><img src="./images/part6-pic3.png" alt=""></div>
										<div class="part6-list-price">价值104元</div>
									</div>
									<div class="col part6-list">
										<div class="part6-list-num">第86-100名</div>
										<div class="part6-list-title">2026公考<br>全科通学营</div>
										<div class="part6-list-pic"><img src="./images/part6-pic4.png" alt=""></div>
										<div class="part6-list-price">价值86元</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-13">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
				<div class="section-content">
					<div class="part1" data-date="2025-9-27">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title1.png" alt=""></div>
								<div class="part-title-sub">就业&备考第一课  从中公杯开始</div>
							</div>
							<div class="part-table tac">
								<div class="part-table-col1">
									<div class="part-table-title">课程内容</div>
									<div class="part-table-content"><span>选方向</span>-探寻优就业方向，公考热门岗位分析</div>
									<div class="part-table-content"><span>找优势</span>-应届生身份报考国省考要把握住这8大优势</div>
									<div class="part-table-content"><span>抓好岗</span>-国省岗位怎么选？选对的更要选发展好的<div class="part-table-line">8月20日一模</div></div>
									<div class="part-table-content"><span>备考逻辑</span>-政治素养备考核心</div>
									<div class="part-table-content"><span>备考逻辑</span>-找对方法轻松解行测理</div>
									<div class="part-table-content"><span>备考逻辑</span>-掌握行测文的作答策略，一眼洞悉题干关系</div>
									<div class="part-table-content"><span>备考逻辑</span>-怎么用材料"拼出申论"（非作文）</div>
									<div class="part-table-content"><span>备考逻辑</span>-文章意为高，笔落惊风雨（作文题）<div class="part-table-line">9月13日二模</div></div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测文 5大技巧点拨</div>
									<div class="part-table-content"><span>行测技巧提升</span>-行测理 6大技巧点拨</div>
									<div class="part-table-content"><span>申论技巧提升</span>-采分要点护抓住，标答技巧有高招<div class="part-table-line">9月27日三模</div></div>
								</div>
								<div class="part-table-col2">
									<div class="part-table-title part-table-title2">上课时间</div>
									<div class="part-table-content">8月12日</div>
									<div class="part-table-content">8月13日</div>
									<div class="part-table-content">8月14日</div>
									<div class="part-table-content">9月8日</div>
									<div class="part-table-content">9月9日</div>
									<div class="part-table-content">9月10日</div>
									<div class="part-table-content">9月11日</div>
									<div class="part-table-content">9月12日</div>
									<div class="part-table-content">9月17日</div>
									<div class="part-table-content">9月18日</div>
									<div class="part-table-content">9月19日</div>
								</div>
								<a href="#" target="_blank" class="part-table-col3 part-table-title part-table-title3">公<br>职<br>就<br>业<br>&<br>备<br>考<br>系<br>列<br>讲<br>座<div class="part-table-pirce bounceY"><img src="./images/price-0.1.png" alt=""></div></a>
							</div>
							<div class="part-book">
								<div class="part-book-link bounceY"><img src="./images/price.png" alt=""></div>
								<div class="row">
									<div class="part-book-title col">中公金牌示范课，邀你来听！</div>
								</div>
								<div class="part-book-content">
									<div class="part-book-content-title"><span>全新设计</span>试听内容</div>
									<div class="part-book-content-subtitle">听10分钟也能让你有所收获</div>
									<div class="part-book-content-info">打破行业试听枯燥、授课太过基础的窘境<br>短时高效感受试题变化，快速获取解题技巧</div>
									<div class="part-book-content-desc">备考不再是拼刷题量的时代<br>拼的是<span>心眼子</span></div>
									<div class="part-book-content-btn part-link part-btn"><img src="./images/part-book-btn.png" alt=""></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-27">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
				<div class="section-content">
					<div class="part8" data-date="2025-9-27">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title2.png" alt=""></div>
								<div class="part-title-sub">万人模考大赛</div>
							</div>
							<div class="part2-subtitle">本校区如未举办线下模考，可参加线上【终极模考】大赛 </div>
							<div class="part-wr clearfix">
								<div class="part-wr-link bounceY"><img src="./images/price-0.png" alt=""></div>
								<div class="part-wr1 fl">
									<div class="part-wr-top">
										<div class="part-wr-title">中公自研AI模考数据库</div>
										<div class="part-wr-subtitle">严选历年试题贴合考情</div>
									</div>
									<div>
										<div class="part-wr-title">明师逐题精讲解析</div>
										<div class="part-wr-subtitle">点拨重难，排除失分弱项</div>
									</div>
								</div>
								<div class="part-wr2 fr">
									<div class="part-wr-top">
										<div class="part-wr-title">严审考题考卷</div>
										<div class="part-wr-subtitle">三审三教保证质量</div>
									</div>
									<div>
										<div class="part-wr-title">考后立即出分</div>
										<div class="part-wr-subtitle">及时了解自身水平</div>
									</div>
								</div>
							</div>
							<div class="part-mk clearfix">
								<div class="fl part-mk-left">
									<div class="row tac">
										<div class="part-mk-qr col"><img src="./images/part-mk-qr.png" alt=""></div>
									</div>
									<div class="row tac">
										<div class="col part-mk-qr-title">扫码下载中公教育APP</div>
									</div>
								</div>
								<div class="fr part-mk-right">
									<div class="row part-mk-item">
										<div class="col part-mk-title">考试时间</div>
										<div class="col part-mk-subtitle">9月27日   [行测] 09:00-11:00   [申论] 14:00-17:00</div>
									</div>
									<div class="row part-mk-item">
										<div class="col part-mk-title">解析时间</div>
										<div class="col part-mk-subtitle">9月28日   [申论] 09:30-12:00   [行测] 13:30-18:00</div>
									</div>
									<div class="row part-mk-item">
										<div class="col part-mk-title">参与方式</div>
										<div class="col part-mk-subtitle">下载中公教育APP，进入国考/本省[刷题] 页面，点击[模考 大赛]或【中公杯万人模考大赛】提前抢座<br>按时间节点等待模考即可</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-27">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
				<div class="section-content">
					<div class="part9" data-date="2025-9-27">
						<div class="part-card">
							<div class="part-title">
								<div><img src="./images/part-title3.png" alt=""></div>
								<div class="part-title-sub">终极模王争霸<br>考场还原高压模拟  校区排名赢大奖</div>
							</div>
							<div class="part-room">
								<div class="part-room-pic1 row tac"><img class="col" src="./images/part-room-pic1.png" alt=""></div>
								<div class="part-room-pic2 row tac part-link"><img class="col" src="./images/part-room-pic2.png" alt=""></div>
								<div class="part-room-pic3 row tac"><img class="col" src="./images/part-room-pic3.png" alt=""></div>
								<div class="part-room-btn part-link part-btn"><img src="./images/part-room-btn.png" alt=""></div>
							</div>
						</div>
					</div>
					<div class="row tac loading" style="margin-top: 200px;"  data-date="2025-9-27">
						<div class="col"><img src="./images/jqqd.png" alt="敬请期待"></div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- fixed -->

	<div class="fixed">
		<div class="fixed-close"></div>
		<a class="fixed-item part-link col"></a>
		<a class="fixed-item part-link col"></a>
		<a class="fixed-item part-link col"></a>
		<a class="fixed-item part-link col"></a>
		<div class="fixed-item col zg_top"></div>
	</div>

	<!-- End fixed -->

	<!-- 省份选择弹框 -->
	<div class="province-modal" id="provinceModal">
		<div class="province-modal-overlay"></div>
		<div class="province-modal-content">
			<div class="province-modal-header">
				<img src="./images/part-teach-btn.png" alt="">
			</div>
			<span class="province-modal-close" id="provinceModalClose"></span>
			<div class="province-modal-body">
				<div class="province-grid">
					<!-- 第一行：8个省份 -->
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-ah.png" alt="安徽二维码"></div>
						<div class="province-name">安徽</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-bj.png" alt="北京二维码"></div>
						<div class="province-name">北京</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-fj.png" alt="福建二维码"></div>
						<div class="province-name">福建</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-gs.png" alt="甘肃二维码"></div>
						<div class="province-name">甘肃</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-gd.png" alt="广东二维码"></div>
						<div class="province-name">广东</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-gx.jpeg" alt="广西二维码"></div>
						<div class="province-name">广西</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-gz.jpeg" alt="贵州二维码"></div>
						<div class="province-name">贵州</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hain.png" alt="海南二维码"></div>
						<div class="province-name">海南</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hb.png" alt="河北二维码"></div>
						<div class="province-name">河北</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hn.png" alt="河南二维码"></div>
						<div class="province-name">河南</div>
					</div>

					<!-- 第二行：8个省份 -->
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hlj.png" alt="黑龙江二维码"></div>
						<div class="province-name">黑龙江</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hub.png" alt="湖北二维码"></div>
						<div class="province-name">湖北</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-hun.png" alt="湖南二维码"></div>
						<div class="province-name">湖南</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-ah.png" alt="吉林二维码"></div>
						<div class="province-name">吉林</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-js.png" alt="江苏二维码"></div>
						<div class="province-name">江苏</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-jx.png" alt="江西二维码"></div>
						<div class="province-name">江西</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-ln.jpeg" alt="辽宁二维码"></div>
						<div class="province-name">辽宁</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-nmg.png" alt="内蒙古二维码"></div>
						<div class="province-name">内蒙古</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-nx.png" alt="宁夏二维码"></div>
						<div class="province-name">宁夏</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-qh.png" alt="青海二维码"></div>
						<div class="province-name">青海</div>
					</div>

					<!-- 第三行：8个省份 -->
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-sd.png" alt="山东二维码"></div>
						<div class="province-name">山东</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-sx.png" alt="山西二维码"></div>
						<div class="province-name">山西</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-shanx.png" alt="陕西二维码"></div>
						<div class="province-name">陕西</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-sh.png" alt="上海二维码"></div>
						<div class="province-name">上海</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-sc.jpeg" alt="四川二维码"></div>
						<div class="province-name">四川</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-tj.png" alt="天津二维码"></div>
						<div class="province-name">天津</div>
					</div>

					<!-- 第四行：6个省份 -->
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-xj.png" alt="新疆二维码"></div>
						<div class="province-name">新疆</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-yn.png" alt="云南二维码"></div>
						<div class="province-name">云南</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-zj.png" alt="浙江二维码"></div>
						<div class="province-name">浙江</div>
					</div>
					<div class="province-item">
						<div class="qr-code"><img src="images/qr-cq.png" alt="重庆二维码"></div>
						<div class="province-name">重庆</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- End 省份选择弹框 -->

	<div class="wrap-footer" style="background-color: #191919; margin-top: 140px;">
		<div class="main" style="width: 1200px;margin:0 auto">
			<h4 style="line-height: 80px;font-weight: bold;font-size: 30px;color: #ffffff;text-align: center;">各地中公
			</h4>
			<iframe
				src="https://www.offcn.com/zg/contact/index.html?bgcolor=ff0000?color=ffffff?btncolor=1e1e1e?btnopacity=1.0?iconcolor=4d4d4d?areabtcolor=ffffff?areatextcolor=bababa?botBtcolor=fff100?botNrcolor=ffffff?libgcolor=003eb5"
				scrolling="no" allowtransparency="true" width="100%" height="488" frameborder="0"></iframe>
		</div>
		<script>
			function createIframe(url, bgcolor, color, btncolor, btnopacity, iconcolor, areabtcolor, areatextcolor, botBtcolor, botNrcolor, libgcolor) {
				$('#addressiframe').html("<iframe src=" + url + "?bgcolor=" + bgcolor + "?color=" + color + "?btncolor=" +
					btncolor + "?btnopacity=" + btnopacity + "?iconcolor=" + iconcolor + "?areabtcolor=" + areabtcolor +
					"?areatextcolor=" + areatextcolor + "?botBtcolor=" + botBtcolor + "?botNrcolor=" + botNrcolor +
					"?libgcolor=" + libgcolor + " height='488' width='1000' scrolling='no' allowtransparency='true' frameborder='0' ></iframe>");
			}
			createIframe("https://www.offcn.com/zg/contact/index.html", "ff0000", "ffffff", "ffa900", "1.0", "4d4d4d", "ffffff", "ffffff", "fff100", "ffffff", "363636");
		</script>
	</div>

	<div class="foot">
		<p> Copyright© 1999-<span id="myYear">2021</span> 北京中公教育科技有限公司 .All Rights Reserved </p>
		<p> 全国统一报名专线：400-6300-999 网校报名：400-900-8885 图书订购：400-6509-705 </p>
		<p> <a target="_blank" href="https://beian.miit.gov.cn/" rel="nofollow">京ICP备10218183号-1</a> <a target="_blank"
				href="https://www.offcn.com/about/zgjj.html">京ICP证161188号</a> <a target="_blank"
				href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010802020593" rel="nofollow">
				<img src="https://www.offcn.com/statics/images/zgimg/gongbei.png"> 京公网安备 11010802020593号 </a>
			出版物经营许可证新出发京批字第直130052号 <a target="_blank" href="https://www.offcn.com/about/yyzz.html">营业执照</a>
			投诉建议：400 6300 999 </p>
		<script>
			nowtime = new Date();
			year = nowtime.getFullYear();
			document.getElementById("myYear").innerText = year;
		</script>
	</div>
	<!--底部结束-->

	<script type="text/javascript" src="js/swiper.js"></script>
	<script type="text/javascript" src="js/index.js?2"></script>

	<!-- 日期控制显示脚本 - jQuery版本 -->
	<script type="text/javascript">
		// 日期控制显示功能 - 使用jQuery实现
		function initDateControl() {
			try {
				// 获取当前日期（零点时间）
				var now = new Date(2025, 9, 17);
				var currentDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
				
				// 调试信息（生产环境可以删除）
				console.log('日期控制检查 - 当前日期:', currentDate.toDateString());

				// 使用jQuery查找所有带有data-date属性的元素
				var $elementsWithDate = $('[data-date]');
				console.log('找到', $elementsWithDate.length, '个带有data-date属性的元素');

				// 使用jQuery的each方法遍历元素
				$elementsWithDate.each(function(index, element) {
					var $element = $(element);
					var dateStr = $element.data('date') || $element.attr('data-date');

					if (!dateStr) {
						console.warn('元素', index, '的data-date属性为空');
						return true; // jQuery中使用return true相当于continue
					}

					// 解析日期字符串 (格式: 2025-9-13)
					var dateParts = dateStr.toString().split('-');
					if (dateParts.length !== 3) {
						console.warn('元素', index, '的日期格式不正确:', dateStr);
						return true;
					}

					var targetYear = parseInt(dateParts[0]);
					var targetMonth = parseInt(dateParts[1]) - 1; // 月份从0开始
					var targetDay = parseInt(dateParts[2]);

					// 验证日期数据
					if (isNaN(targetYear) || isNaN(targetMonth) || isNaN(targetDay)) {
						console.warn('元素', index, '的日期数据无效:', dateStr);
						return true;
					}

					var targetDate = new Date(targetYear, targetMonth, targetDay);

					// 判断是否到达目标日期
					var isDateReached = currentDate >= targetDate;

					// 调试信息
					console.log('元素', index, '- 目标日期:', targetDate.toDateString(), '是否到达:', isDateReached, '类名:', $element.attr('class'));

					// 使用jQuery检查是否有loading类
					if ($element.hasClass('loading')) {
						if (isDateReached) {
							// 到达日期，隐藏loading元素
							$element.hide();
							console.log('隐藏loading元素:', dateStr);
						} else {
							// 未到达日期，显示loading元素
							$element.show();
							console.log('显示loading元素:', dateStr);
						}
					} else {
						// 这是主要内容元素
						if (isDateReached) {
							// 到达日期，显示主要内容
							$element.show();
							console.log('显示主要内容:', dateStr);
						} else {
							// 未到达日期，隐藏主要内容
							$element.hide();
							console.log('隐藏主要内容:', dateStr);
						}
					}
				});

				console.log('日期控制检查完成');
			} catch (error) {
				console.error('日期控制功能出错:', error);
			}
		}

		// 使用jQuery的document ready事件
		$(document).ready(function() {
			console.log('页面加载完成，开始执行日期控制');
			initDateControl();
		});

		// 每分钟检查一次（用于实时更新）
		setInterval(function() {
			console.log('定时检查日期控制');
			initDateControl();
		}, 60000);

		// 添加手动触发函数（调试用）
		window.triggerDateControl = function() {
			console.log('手动触发日期控制检查');
			initDateControl();
		};
	</script>

	<!--2022新版统计代码-开始-->
	<script>
		if (!navigator.userAgent.match(/(iPhone|iPad|iPod|iOS|Android)/i)) {
			// pc
			var _hmt = _hmt || [];
			(function () {
				var hm = document.createElement("script");
				hm.src = "https://hm.baidu.com/hm.js?80650b2f37157e0c6c8938a3129420b5";
				var s = document.getElementsByTagName("script")[0];
				s.parentNode.insertBefore(hm, s);
			})();
		} else {
			// wap
			var _hmt = _hmt || [];
			(function () {
				var hm = document.createElement("script");
				hm.src = "https://hm.baidu.com/hm.js?1ce3de0d519ae17a801f04c7e698e3bb";
				var s = document.getElementsByTagName("script")[0];
				s.parentNode.insertBefore(hm, s);
			})();
		}
	</script>
	<!--2022新版统计代码-结束-->


	<!-- 公共_底部_百度统计代码|咨询js-->
	<script type="text/javascript" src="https://statics.offcn.com/common/pc/js/bdtj/offcn/bdtj_offcn_pc.js"></script>
	<script type="text/javascript" src="https://statics.offcn.com/common/pc/js/bdtj/bdbot.js"></script>

</body>

</html>

